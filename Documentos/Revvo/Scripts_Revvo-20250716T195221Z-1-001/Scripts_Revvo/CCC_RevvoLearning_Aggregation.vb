#If Not SCRIPTDEBUGGER Then
References VI.DataImport.dll

Imports System.Collections.Generic
Imports System.IO
Imports System.Globalization
Imports VI.DB.Entities
Imports VI.DB.Specialized
Imports VI.DataImport
Imports Newtonsoft.Json.Linq
Imports Newtonsoft.Json
Imports System.Net
Imports System.Text.RegularExpressions
Imports System.Threading
#End If



Public Function CCC_RevvoLearning_Aggregation(Optional ByVal pageSize As Integer = 20, Optional ByVal environment As String = "DASA") As String
    Dim logMessage As String = ""
    Dim totalProcessed As Integer = 0
    Dim totalCreated As Integer = 0
    Dim totalUpdated As Integer = 0
    Dim totalErrors As Integer = 0

    ' Obter endpoints baseados no ambiente
    Dim endpoints As Dictionary(Of String, String) = CCC_RevvoLearning_GetEndpointsByEnvironment(environment)
    Dim userApiBaseUrl As String = endpoints("URL")

    Try
        ' Validar se o ambiente é válido (sem fallback)
        If String.IsNullOrEmpty(userApiBaseUrl) Or endpoints("Environment") = "UNKNOWN" Or endpoints("Environment") = "ERROR" Then
            logMessage = String.Format("[{0}] ERRO: Ambiente inválido ou não encontrado: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), environment)
            Console.WriteLine(logMessage)
            Return "ERRO: Ambiente inválido - " & environment
        End If

        logMessage = String.Format("[{0}] Iniciando agregação de usuários RevvoLearning - Ambiente: {1}, PageSize: {2}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), endpoints("Environment"), pageSize)
        Console.WriteLine(logMessage)

        logMessage = String.Format("[{0}] Obtendo token de autenticação...", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
        Console.WriteLine(logMessage)

        Dim accessToken As String = CCC_RevvoLearning_AuthByEnvironment(environment)
        
        If String.IsNullOrEmpty(accessToken) Then
            logMessage = String.Format("[{0}] ERRO: Falha na autenticação - Token não obtido", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
            Console.WriteLine(logMessage)
            Return "ERRO: Falha na autenticação"
        End If

        logMessage = String.Format("[{0}] Token obtido com sucesso", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
        Console.WriteLine(logMessage)

        Dim hasNextPage As Boolean = True
        Dim nextPageUrl As String = userApiBaseUrl & "?_limit=" & pageSize.ToString()
        Dim currentPage As Integer = 1

        While hasNextPage
            logMessage = String.Format("[{0}] Processando página {1}...", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), currentPage)
            Console.WriteLine(logMessage)

            Dim apiResponseString As String = String.Empty
            
            Try
                Using client As New WebClient()
                    client.Headers(HttpRequestHeader.Authorization) = "Bearer " & accessToken
                    client.Headers(HttpRequestHeader.ContentType) = "application/json"
                    apiResponseString = client.DownloadString(nextPageUrl)
                End Using
            Catch webEx As WebException
                logMessage = String.Format("[{0}] ERRO ao buscar página {1}: {2}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), currentPage, webEx.Message)
                Console.WriteLine(logMessage)
                totalErrors += 1
                Exit While
            End Try

            Try
                Dim jsonResponse As JObject = JObject.Parse(apiResponseString)
                Dim embeddedUsers As JArray = Nothing
                
                If jsonResponse("_embedded") IsNot Nothing AndAlso jsonResponse("_embedded")("users") IsNot Nothing Then
                    embeddedUsers = CType(jsonResponse("_embedded")("users"), JArray)
                End If

                If embeddedUsers IsNot Nothing AndAlso embeddedUsers.HasValues Then
                    logMessage = String.Format("[{0}] Encontrados {1} usuários na página {2}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), embeddedUsers.Count, currentPage)
                    Console.WriteLine(logMessage)

                    For userIndex As Integer = 0 To embeddedUsers.Count - 1
                        Dim userJson As JObject = CType(embeddedUsers(userIndex), JObject)
                        
                        Dim usernameApi As String = ""
                        If userJson("username") IsNot Nothing Then
                            usernameApi = userJson("username").ToString()
                        End If

                        If Not String.IsNullOrEmpty(usernameApi) Then
                            Try
                                Dim processResult As String = ProcessSingleUser(userJson)
                                totalProcessed += 1
                                
                                If processResult.StartsWith("CREATED") Then
                                    totalCreated += 1
                                ElseIf processResult.StartsWith("UPDATED") Then
                                    totalUpdated += 1
                                ElseIf processResult.StartsWith("ERRO") Then
                                    totalErrors += 1
                                End If

                                ' Pausa pequena para não sobrecarregar
                                Thread.Sleep(100)

                            Catch ex As Exception
                                logMessage = String.Format("[{0}] ERRO ao processar usuário {1}: {2}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), usernameApi, ex.Message)
                                Console.WriteLine(logMessage)
                                totalErrors += 1
                            End Try
                        End If
                    Next

                    ' Verificar próxima página
                    Dim nextLink As JObject = Nothing
                    If jsonResponse("_links") IsNot Nothing AndAlso jsonResponse("_links")("next") IsNot Nothing Then
                        nextLink = CType(jsonResponse("_links")("next"), JObject)
                    End If

                    If nextLink IsNot Nothing AndAlso nextLink("href") IsNot Nothing Then
                        nextPageUrl = nextLink("href").ToString()
                        hasNextPage = True
                        currentPage += 1
                        
                        ' Pausa entre páginas
                        Thread.Sleep(500)
                    Else
                        hasNextPage = False
                    End If
                Else
                    logMessage = String.Format("[{0}] Nenhum usuário encontrado na página {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), currentPage)
                    Console.WriteLine(logMessage)
                    hasNextPage = False
                End If

            Catch jsonEx As Exception
                logMessage = String.Format("[{0}] ERRO ao processar JSON da página {1}: {2}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), currentPage, jsonEx.Message)
                Console.WriteLine(logMessage)
                totalErrors += 1
                hasNextPage = False
            End Try
        End While

        logMessage = String.Format("[{0}] Agregação concluída - Total: {1} | Criados: {2} | Atualizados: {3} | Erros: {4}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), totalProcessed, totalCreated, totalUpdated, totalErrors)
        Console.WriteLine(logMessage)

        Return String.Format("SUCESSO: Processados {0} usuários (Criados: {1}, Atualizados: {2}, Erros: {3})", totalProcessed, totalCreated, totalUpdated, totalErrors)

    Catch generalEx As Exception
        logMessage = String.Format("[{0}] ERRO CRÍTICO na agregação: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), generalEx.Message)
        Console.WriteLine(logMessage)
        Return "ERRO CRÍTICO: " & generalEx.Message
    End Try
End Function

Private Function ProcessSingleUser(ByVal userJson As JObject) As String
    Try
        Dim usernameApi As String = ""
        If userJson("username") IsNot Nothing Then
            usernameApi = userJson("username").ToString()
        End If

        If String.IsNullOrEmpty(usernameApi) Then
            Return "ERRO: Username vazio"
        End If

        ' Limpar username
        usernameApi = Regex.Replace(usernameApi, "[^a-zA-Z0-9\._-]", "").ToLower()

        Dim f = Session.SqlFormatter
        Dim unsAccountBQuery = f.UidComparison("CCC_CustomProperty11", usernameApi)
        Dim unsAccountBCol As IEntityCollection = Session.Source().GetCollection(Query.From("UNSAccountB").Where(unsAccountBQuery).SelectNonLobs)

        Dim unsAccount As IEntity
        Dim isNewAccount As Boolean = False

        If unsAccountBCol.Count = 0 Then
            unsAccount = Session.Source().CreateNew("UNSAccountB")
            isNewAccount = True
        Else
            unsAccount = unsAccountBCol(0)
        End If

        ' Mapear campos conforme usado no CreateUser
        Dim mappingKeys() As String = {"username", "firstname", "lastname", "email", "id"}
        Dim mappingValues() As String = {"CCC_CustomProperty11", "FirstName", "LastName", "CCC_CustomProperty15", "CCC_CustomProperty24"}
        
        For mappingIndex As Integer = 0 To mappingKeys.Length - 1
            Dim apiFieldName As String = mappingKeys(mappingIndex)
            Dim oimFieldName As String = mappingValues(mappingIndex)
            
            If userJson(apiFieldName) IsNot Nothing Then
                Dim apiValue As String = userJson(apiFieldName).ToString()
                
                Select Case oimFieldName
                    Case "CCC_CustomProperty11"
                        apiValue = Regex.Replace(apiValue, "[^a-zA-Z0-9\._-]", "").ToLower()
                    Case "CCC_CustomProperty15"
                        If Not Regex.IsMatch(apiValue, "^[^@\s]+@[^@\s]+\.[^@\s]+$") Then
                            Continue For
                        End If
                End Select
                
                unsAccount.PutValue(oimFieldName, apiValue)
            End If
        Next

        If isNewAccount Then
            unsAccount.PutValue("UID_UNSRootB", "ab43d0a4-969c-4793-a224-d0c6ecdd7187")
            
            Dim firstName As String = ""
            Dim lastName As String = ""
            If userJson("firstname") IsNot Nothing Then firstName = userJson("firstname").ToString()
            If userJson("lastname") IsNot Nothing Then lastName = userJson("lastname").ToString()
            
            unsAccount.PutValue("DisplayName", firstName & " " & lastName)
			
        End If

        unsAccount.Save(Session)

        Return If(isNewAccount, "CREATED: " & usernameApi, "UPDATED: " & usernameApi)

    Catch ex As Exception
        Return "ERRO: " & ex.Message
    End Try
End Function
