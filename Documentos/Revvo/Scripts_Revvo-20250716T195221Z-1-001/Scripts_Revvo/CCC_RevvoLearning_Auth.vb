Imports Newtonsoft.Json.Linq
Imports Newtonsoft.Json
Imports System.Net
Imports VI.DB.Entities
Imports VI.DB.Specialized

' Função auxiliar para determinar URLs baseadas no AccountDef
Public Function CCC_RevvoLearning_GetEndpoints(ByVal UID_UNSAccountB As String) As Dictionary(Of String, String)
    Dim endpoints As New Dictionary(Of String, String)

    Try
        Dim f = Session.SqlFormatter
        Dim queryUNSAccountB = f.Uid<PERSON>omparison("UID_UNSAccountB", UID_UNSAccountB)
        Dim UNSAccountBCol As IEntityCollection = Session.Source().GetCollection(Query.From("UNSAccountB").Where(queryUNSAccountB).SelectNonLobs)

        If UNSAccountBCol.Count > 0 Then
            Dim UNSAccountB As IEntity = UNSAccountBCol(0)
            Dim AccountDef As String = UNSAccountB.GetValue("UID_TSBAccountDef").String

            ' Determinar ambiente baseado no AccountDef - SEM FALLBACK
            If AccountDef = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\UIDAccountDefDASA") Then
                endpoints("TokenURL") = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\TokenURL")
                endpoints("URL") = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\URL")
                endpoints("ClientId") = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\ClientId")
                endpoints("ClientSecret") = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\ClientSecret")
                endpoints("Environment") = "DASA"
            ElseIf AccountDef = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\UIDAccountDefAmericasFunc") Then
                endpoints("TokenURL") = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\TokenURL")
                endpoints("URL") = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\URL")
                endpoints("ClientId") = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\ClientId")
                endpoints("ClientSecret") = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\ClientSecret")
                endpoints("Environment") = "AmericasFunc"
            ElseIf AccountDef = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\UIDAccountDefAmericasMed") Then
                endpoints("TokenURL") = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\TokenURL")
                endpoints("URL") = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\URL")
                endpoints("ClientId") = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\ClientId")
                endpoints("ClientSecret") = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\ClientSecret")
                endpoints("Environment") = "AmericasMed"
            Else
                ' Não há fallback - retorna endpoints vazios se não encontrar correspondência
                endpoints("TokenURL") = ""
                endpoints("URL") = ""
                endpoints("ClientId") = ""
                endpoints("ClientSecret") = ""
                endpoints("Environment") = "UNKNOWN"
                Console.WriteLine("ERRO: AccountDef não reconhecido: " & AccountDef)
            End If
        Else
            Console.WriteLine("ERRO: Conta não encontrada para UID: " & UID_UNSAccountB)
            endpoints("TokenURL") = ""
            endpoints("URL") = ""
            endpoints("ClientId") = ""
            endpoints("ClientSecret") = ""
            endpoints("Environment") = "NOT_FOUND"
        End If

    Catch ex As Exception
        Console.WriteLine("Erro ao determinar endpoints: " & ex.Message)
        endpoints("TokenURL") = ""
        endpoints("URL") = ""
        endpoints("ClientId") = ""
        endpoints("ClientSecret") = ""
        endpoints("Environment") = "ERROR"
    End Try

    Return endpoints
End Function

Public Function CCC_RevvoLearning_Auth(ByVal UID_UNSAccountB As String) As String
    Dim accessToken As String = Nothing ' Variável para armazenar o token de acesso

    Try
        ' Obter endpoints baseados no UID_UNSAccountB
        Dim endpoints As Dictionary(Of String, String) = CCC_RevvoLearning_GetEndpoints(UID_UNSAccountB)
        Dim clientId As String = endpoints("ClientId")
        Dim clientSecret As String = endpoints("ClientSecret")
        Dim tokenURL As String = endpoints("TokenURL")
        Dim environment As String = endpoints("Environment")

        Console.WriteLine("Usando ambiente: " & environment & " - TokenURL: " & tokenURL)

        ' Validar se os endpoints são válidos (sem fallback)
        If String.IsNullOrEmpty(tokenURL) Or String.IsNullOrEmpty(clientId) Or String.IsNullOrEmpty(clientSecret) Then
            Console.WriteLine("ERRO: Endpoints inválidos ou não encontrados para o ambiente: " & environment)
            Return Nothing
        End If

        Using client As New WebClient()
            ' Define o Content-Type para indicar que o corpo da requisição é JSON
            client.Headers(HttpRequestHeader.ContentType) = "application/json"

            ' Prepara os dados JSON para a requisição de autenticação
            ' É crucial que a string JSON seja formatada corretamente com aspas duplas escapadas.
            Dim jsonData As String = String.Format(
                "{{""grant_type"": ""client_credentials"", ""client_id"": ""{0}"", ""client_secret"": ""{1}""}}",
                clientId, clientSecret
            )

            ' Converte a string JSON para um array de bytes usando UTF8, que é padrão para JSON
            Dim requestBytes As Byte() = System.Text.Encoding.UTF8.GetBytes(jsonData)

            ' Realiza a requisição POST para o endpoint de token
            Dim responseBytes As Byte() = client.UploadData(tokenURL, "POST", requestBytes)

            ' Converte a resposta em bytes para uma string usando UTF8
            Dim responseString As String = System.Text.Encoding.UTF8.GetString(responseBytes)

            ' Desserializa a string JSON da resposta para um objeto JObject
            Dim jsonResponse As JObject = JObject.Parse(responseString)

            ' Verifica se o objeto JSON e o campo 'access_token' existem antes de tentar acessá-los
            If jsonResponse IsNot Nothing AndAlso jsonResponse("access_token") IsNot Nothing Then
                accessToken = jsonResponse("access_token").ToString()

                Console.WriteLine("Token de acesso obtido com sucesso.")
            Else
                Console.WriteLine("Resposta JSON não contém 'access_token' ou está vazia.")
                Console.WriteLine("Resposta completa: " & responseString)
            End If

        End Using ' Garante que o WebClient seja descartado corretamente

    Catch ex As WebException
        ' Captura exceções específicas de requisições web (e.g., erros HTTP 4xx, 5xx)
        Console.WriteLine("Erro na requisição web durante a autenticação: " & ex.Message)
        If ex.Response IsNot Nothing Then
            Using reader As New System.IO.StreamReader(ex.Response.GetResponseStream())
                Dim errorResponse As String = reader.ReadToEnd()
                Console.WriteLine("Resposta de erro do servidor: " & errorResponse)
            End Using
        End If
        ' Em caso de erro, retorna Nothing para indicar falha na obtenção do token
        Return Nothing
    Catch ex As Exception
        ' Captura outras exceções genéricas
        Console.WriteLine("Ocorreu um erro inesperado durante a autenticação: " & ex.Message)
        ' Em caso de erro, retorna Nothing
        Return Nothing
    End Try

    Return accessToken ' Retorna o token de acesso (ou Nothing se houve erro)
End Function

' Função de autenticação por ambiente (para Aggregation)
Public Function CCC_RevvoLearning_AuthByEnvironment(ByVal environment As String) As String
    Dim accessToken As String = Nothing

    Try
        ' Obter endpoints baseados no ambiente
        Dim endpoints As Dictionary(Of String, String) = CCC_RevvoLearning_GetEndpointsByEnvironment(environment)
        Dim clientId As String = endpoints("ClientId")
        Dim clientSecret As String = endpoints("ClientSecret")
        Dim tokenURL As String = endpoints("TokenURL")

        Console.WriteLine("Usando ambiente: " & endpoints("Environment") & " - TokenURL: " & tokenURL)

        ' Validar se os endpoints são válidos (sem fallback)
        If String.IsNullOrEmpty(tokenURL) Or String.IsNullOrEmpty(clientId) Or String.IsNullOrEmpty(clientSecret) Then
            Console.WriteLine("ERRO: Endpoints inválidos ou não encontrados para o ambiente: " & environment)
            Return Nothing
        End If

        Using client As New WebClient()
            client.Headers(HttpRequestHeader.ContentType) = "application/json"

            Dim jsonData As String = String.Format(
                "{{""grant_type"": ""client_credentials"", ""client_id"": ""{0}"", ""client_secret"": ""{1}""}}",
                clientId, clientSecret
            )

            Dim requestBytes As Byte() = System.Text.Encoding.UTF8.GetBytes(jsonData)
            Dim responseBytes As Byte() = client.UploadData(tokenURL, "POST", requestBytes)
            Dim responseString As String = System.Text.Encoding.UTF8.GetString(responseBytes)

            Dim jsonResponse As JObject = JObject.Parse(responseString)

            If jsonResponse IsNot Nothing AndAlso jsonResponse("access_token") IsNot Nothing Then
                accessToken = jsonResponse("access_token").ToString()
                Console.WriteLine("Token de acesso obtido com sucesso.")
            Else
                Console.WriteLine("Resposta JSON não contém 'access_token' ou está vazia.")
                Console.WriteLine("Resposta completa: " & responseString)
            End If

        End Using

    Catch ex As WebException
        Console.WriteLine("Erro na requisição web durante a autenticação: " & ex.Message)
        If ex.Response IsNot Nothing Then
            Using reader As New System.IO.StreamReader(ex.Response.GetResponseStream())
                Dim errorResponse As String = reader.ReadToEnd()
                Console.WriteLine("Resposta de erro do servidor: " & errorResponse)
            End Using
        End If
        Return Nothing
    Catch ex As Exception
        Console.WriteLine("Ocorreu um erro inesperado durante a autenticação: " & ex.Message)
        Return Nothing
    End Try

    Return accessToken
End Function

' Função auxiliar para obter endpoints por ambiente (para Aggregation)
Public Function CCC_RevvoLearning_GetEndpointsByEnvironment(ByVal environment As String) As Dictionary(Of String, String)
    Dim endpoints As New Dictionary(Of String, String)

    Try
        Select Case environment.ToUpper()
            Case "DASA"
                endpoints("TokenURL") = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\TokenURL")
                endpoints("URL") = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\URL")
                endpoints("ClientId") = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\ClientId")
                endpoints("ClientSecret") = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\DASA\ClientSecret")
                endpoints("Environment") = "DASA"
            Case "AMERICASFUNC"
                endpoints("TokenURL") = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\TokenURL")
                endpoints("URL") = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\URL")
                endpoints("ClientId") = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\ClientId")
                endpoints("ClientSecret") = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasFunc\ClientSecret")
                endpoints("Environment") = "AmericasFunc"
            Case "AMERICASMED"
                endpoints("TokenURL") = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\TokenURL")
                endpoints("URL") = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\URL")
                endpoints("ClientId") = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\ClientId")
                endpoints("ClientSecret") = Connection.GetConfigParm("Custom\CustomTargetSystem\RevvoLearning\AmericasMed\ClientSecret")
                endpoints("Environment") = "AmericasMed"
            Case Else
                ' Não há fallback - retorna endpoints vazios se não encontrar correspondência
                endpoints("TokenURL") = ""
                endpoints("URL") = ""
                endpoints("ClientId") = ""
                endpoints("ClientSecret") = ""
                endpoints("Environment") = "UNKNOWN"
                Console.WriteLine("ERRO: Ambiente não reconhecido: " & environment)
        End Select

    Catch ex As Exception
        Console.WriteLine("Erro ao determinar endpoints por ambiente: " & ex.Message)
        endpoints("TokenURL") = ""
        endpoints("URL") = ""
        endpoints("ClientId") = ""
        endpoints("ClientSecret") = ""
        endpoints("Environment") = "ERROR"
    End Try

    Return endpoints
End Function
